livekit/plugins/turn_detector/__init__.py,sha256=EMiSKcWaes7UXMbKrPeUrK1ZqeRCT4k2nv9YIjRLfhY,1724
livekit/plugins/turn_detector/__pycache__/__init__.cpython-312.pyc,,
livekit/plugins/turn_detector/__pycache__/base.cpython-312.pyc,,
livekit/plugins/turn_detector/__pycache__/english.cpython-312.pyc,,
livekit/plugins/turn_detector/__pycache__/log.cpython-312.pyc,,
livekit/plugins/turn_detector/__pycache__/models.cpython-312.pyc,,
livekit/plugins/turn_detector/__pycache__/multilingual.cpython-312.pyc,,
livekit/plugins/turn_detector/__pycache__/version.cpython-312.pyc,,
livekit/plugins/turn_detector/base.py,sha256=dj42fmnYtm26cOWM666HB4FbeaoIkNVheFsJSeoYz5w,8280
livekit/plugins/turn_detector/english.py,sha256=zgrAjg8By6onDLoXFHf99pXXJU4cO0mptrHCkYLOUeg,842
livekit/plugins/turn_detector/log.py,sha256=a0uGBu1dmzh0XgEi4l5dgH3zb0bj86RGP-i-WtDWNRY,76
livekit/plugins/turn_detector/models.py,sha256=slTAnlBdUOyrGP7jSSuSNdcLuBzhSmt9IAHLQ5TdzIY,245
livekit/plugins/turn_detector/multilingual.py,sha256=wGh0rzO_5kNtAjcMiyfCo_2OFFJ3176df0gEMuAaZj0,3555
livekit/plugins/turn_detector/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
livekit/plugins/turn_detector/version.py,sha256=dVdBNJ1YUktIv-jJZo4BlufiZyTAqjXbXjjf6egLPmU,599
livekit_plugins_turn_detector-1.2.5.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
livekit_plugins_turn_detector-1.2.5.dist-info/METADATA,sha256=eBgcIIl_Xu7pHQTI_ISpgHm-X-vZS-2dgmlAA0ZGbio,3797
livekit_plugins_turn_detector-1.2.5.dist-info/RECORD,,
livekit_plugins_turn_detector-1.2.5.dist-info/WHEEL,sha256=qtCwoSJWgHk21S1Kb4ihdzI2rlJ1ZKaIurTj_ngOhyQ,87
