from dotenv import load_dotenv
import os
from livekit import agents
from livekit.agents import Agent<PERSON>ess<PERSON>, Agent, RoomInputOptions
from livekit.plugins import (
    openai,
    volcengine,
    azure,
    noise_cancellation,
    silero,
)
from livekit.plugins.turn_detector.multilingual import MultilingualModel

load_dotenv()


class Assistant(Agent):
    def __init__(self) -> None:
        super().__init__(instructions="You are a helpful voice AI assistant.")


async def entrypoint(ctx: agents.JobContext):
    session = AgentSession(

        stt=azure.STT(
        language=["zh-CN"],
    ),

        llm=openai.LLM(
        model="deepseek-chat",
        base_url="https://api.deepseek.com",
        api_key=os.environ.get("DEEPSEEK_API_KEY"),
        temperature=0.7,
    ),
    
        tts=volcengine.TTS(
            app_id="1266811133",
            cluster="volcano_tts",
            voice="BV025_streaming",
            sample_rate=24000,
            encoding="mp3"
        ),
        
        vad=silero.VAD.load(),
        turn_detection=MultilingualModel(),
    )

    await session.start(
        room=ctx.room,
        agent=Assistant(),
        room_input_options=RoomInputOptions(
            # LiveKit Cloud enhanced noise cancellation
            # - If self-hosting, omit this parameter
            # - For telephony applications, use `BVCTelephony` for best results
            noise_cancellation=noise_cancellation.BVC(), 
        ),
    )

    await session.generate_reply(
        instructions="Greet the user and offer your assistance."
    )


if __name__ == "__main__":
    agents.cli.run_app(agents.WorkerOptions(entrypoint_fnc=entrypoint))